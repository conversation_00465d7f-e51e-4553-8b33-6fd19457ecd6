I want to build a mobile application that show the stock chart differently.
1. the application can show the different stock daily return at a page.
2. the main goal is let the graph show differently and beauty, so that user show see the stock price at anywhere and other people does not know they are viewing the stock.
3. user can set different mode to show the graph, for example, user can choose the icon for the stock, then the height of the icon indicate that the daily profit/return rate. other people just see the icon of the screen, so that user can see the multiple stock performance if office and other places.



target front end -- expo react native
platform -- android first
application language -- english first