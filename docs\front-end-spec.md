# VeilView 前端设计规范

## 设计理念

### 核心设计原则
1. **隐蔽性优先**：每个界面都必须看起来像真实的非股票应用
2. **美观实用**：伪装界面本身必须具有独立的美学价值
3. **直觉操作**：复杂的功能通过简单的交互实现
4. **情境适应**：设计能够适应不同的使用环境和场景
5. **无缝切换**：不同模式间的切换要自然流畅

### 设计目标
- 创造完美的视觉伪装效果
- 提供优秀的用户体验
- 确保功能的易用性和可发现性
- 建立独特的品牌识别

## 伪装模式设计

### 1. 天气模式设计

#### 视觉设计
- **整体风格**：现代简约天气应用风格
- **主色调**：天蓝色系 (#87CEEB, #4682B4, #1E90FF)
- **辅助色**：白色、浅灰色、橙色（日出日落）
- **字体**：SF Pro Display (iOS) / Roboto (Android)

#### 数据映射规则
```
股票价格 → 温度显示
- 价格范围映射到合理温度范围 (0°C - 40°C)
- 涨跌幅度影响温度变化速度

股票趋势 → 天气状况
- 上涨 > 5% → 晴天 ☀️
- 上涨 1-5% → 多云转晴 ⛅
- 平稳 ±1% → 多云 ☁️
- 下跌 1-5% → 阴天 🌫️
- 下跌 > 5% → 雨天 🌧️

成交量 → 风速
- 高成交量 → 强风
- 低成交量 → 微风

波动率 → 湿度
- 高波动 → 高湿度
- 低波动 → 低湿度
```

#### 界面布局
```
┌─────────────────────────┐
│  📍 当前位置 (股票名称)    │
│                         │
│      ☀️                │
│     25°C               │
│   (股票价格)             │
│                         │
│  今日: 晴天 18°-28°      │
│  (今日涨跌幅)            │
│                         │
│  ┌─────┬─────┬─────┐    │
│  │明天 │后天 │大后天│    │
│  │☁️  │🌧️ │☀️  │    │
│  │22° │19° │26° │    │
│  └─────┴─────┴─────┘    │
│                         │
│  风速: 15km/h (成交量)   │
│  湿度: 65% (波动率)      │
└─────────────────────────┘
```

### 2. 健康模式设计

#### 视觉设计
- **整体风格**：医疗健康应用风格
- **主色调**：健康绿色系 (#00C851, #4CAF50, #8BC34A)
- **辅助色**：红色（警告）、蓝色（信息）、白色
- **图标**：医疗健康相关图标集

#### 数据映射规则
```
股票价格 → 心率 (BPM)
- 价格映射到正常心率范围 (60-100 BPM)
- 价格变化影响心率变化

股票趋势 → 健康状态
- 大涨 → 优秀 💚
- 小涨 → 良好 💛  
- 平稳 → 正常 🤍
- 小跌 → 注意 🧡
- 大跌 → 警告 ❤️

成交量 → 步数
- 高成交量 → 高步数
- 低成交量 → 低步数

市值 → 卡路里消耗
```

#### 界面布局
```
┌─────────────────────────┐
│     健康概览             │
│                         │
│  ❤️ 心率                │
│     78 BPM             │
│   (股票价格)             │
│     💚 优秀             │
│                         │
│  🚶 今日步数             │
│     8,547 步           │
│   (成交量)               │
│                         │
│  🔥 卡路里               │
│     2,340 cal          │
│   (市值)                 │
│                         │
│  📊 本周趋势             │
│  ▁▃▅▇▆▄▂ (价格走势)      │
└─────────────────────────┘
```

### 3. 艺术模式设计

#### 视觉设计
- **整体风格**：现代艺术画廊风格
- **主色调**：动态色彩，根据数据变化
- **背景**：深色或浅色可切换
- **动画**：流畅的几何变换动画

#### 数据映射规则
```
股票价格 → 几何图形大小
- 价格高低影响图形尺寸

股票趋势 → 颜色变化
- 上涨 → 暖色调 (红、橙、黄)
- 下跌 → 冷色调 (蓝、紫、绿)
- 平稳 → 中性色调 (灰、白、黑)

成交量 → 图形数量/密度
波动率 → 动画速度
市值 → 图形复杂度
```

## 交互设计

### 手势系统

#### 基础手势
- **单击**：显示详细信息（3秒后自动隐藏）
- **双击**：切换伪装模式
- **长按**：进入设置模式
- **左滑**：切换到下一个股票
- **右滑**：切换到上一个股票
- **下拉**：刷新数据

#### 安全手势（密码手势）
- **自定义手势序列**：用户可设置复杂的手势组合
- **紧急隐藏**：三指同时点击立即切换到伪装模式
- **假手势**：错误手势显示假数据

### 导航设计

#### 隐蔽导航
- **伪装模式下**：导航元素完全隐藏或伪装成其他功能
- **设置入口**：通过特定手势或隐藏按钮进入
- **快速切换**：底部隐藏的模式切换栏

#### 设置界面
```
┌─────────────────────────┐
│  ⚙️ 设置                │
│                         │
│  📱 伪装模式             │
│  ├ 天气模式 ✓           │
│  ├ 健康模式             │
│  └ 艺术模式             │
│                         │
│  📊 监控股票             │
│  ├ AAPL - Apple        │
│  ├ GOOGL - Google      │
│  └ + 添加股票           │
│                         │
│  🔒 安全设置             │
│  ├ 手势密码             │
│  ├ 生物识别             │
│  └ 自动锁定             │
│                         │
│  🎨 个性化               │
│  ├ 主题颜色             │
│  ├ 动画效果             │
│  └ 字体大小             │
└─────────────────────────┘
```

## 响应式设计

### 屏幕适配
- **手机端**：主要设计目标，优化单手操作
- **平板端**：利用更大屏幕显示更多信息
- **横屏模式**：重新布局以适应宽屏显示

### 设备兼容性
- **iOS设备**：iPhone 8及以上，iPad Air及以上
- **Android设备**：5.5寸以上屏幕，分辨率1080p以上
- **刘海屏适配**：安全区域适配
- **折叠屏支持**：未来版本考虑

## 动画与过渡

### 模式切换动画
- **淡入淡出**：模式间平滑过渡
- **变形动画**：数据元素的自然变换
- **加载动画**：符合当前伪装模式的加载效果

### 数据更新动画
- **数值变化**：平滑的数字滚动效果
- **图表更新**：流畅的图形变化动画
- **状态变化**：颜色和图标的渐变效果

## 可访问性设计

### 无障碍支持
- **VoiceOver/TalkBack**：屏幕阅读器支持
- **动态字体**：支持系统字体大小设置
- **高对比度**：支持高对比度模式
- **色盲友好**：使用色盲友好的颜色方案

### 多语言支持
- **本地化**：支持中文、英文等主要语言
- **RTL支持**：支持从右到左的语言
- **文化适应**：根据地区调整设计元素

## 品牌设计

### Logo设计
- **概念**：结合隐蔽性和股票监控的双重含义
- **图形**：抽象的眼睛或面具元素
- **颜色**：深蓝色主色调，体现专业和隐蔽

### 图标系统
- **统一风格**：现代简约线性图标
- **伪装图标**：每种模式都有对应的伪装图标集
- **状态图标**：清晰的状态指示图标

## 开发规范

### 设计系统
- **颜色规范**：定义完整的颜色系统
- **字体规范**：字体大小、行高、字重规范
- **间距规范**：统一的间距和布局网格
- **组件库**：可复用的UI组件库

### 设计交付
- **设计稿**：Figma高保真设计稿
- **切图资源**：@1x, @2x, @3x图片资源
- **动效规范**：详细的动画参数说明
- **开发标注**：完整的开发标注文档

---

*本前端规范基于BMAD方法论创建，为VeilView提供完整的用户体验和界面设计指导。*
